<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Editor</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        .editor-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .editor-workspace {
            display: grid;
            grid-template-columns: 250px 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .editor-tools {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            height: fit-content;
        }
        
        .canvas-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        #editor-canvas {
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            cursor: crosshair;
            max-width: 100%;
            height: auto;
        }
        
        .tool-group {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .tool-group:last-child {
            border-bottom: none;
        }
        
        .tool-group h3 {
            font-size: 1rem;
            margin-bottom: 10px;
            color: #4a5568;
        }
        
        .tool-button {
            width: 100%;
            padding: 10px;
            margin-bottom: 8px;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9rem;
        }
        
        .tool-button:hover {
            border-color: #667eea;
            background: #f7fafc;
        }
        
        .tool-button.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        .color-input {
            width: 100%;
            height: 40px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .size-slider {
            width: 100%;
            margin: 10px 0;
        }
        
        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            justify-content: center;
        }
        
        @media (max-width: 768px) {
            .editor-workspace {
                grid-template-columns: 1fr;
            }
            
            .editor-tools {
                order: 2;
            }
        }
    </style>
</head>
<body>
    <div class="editor-container">
        <header style="text-align: center; color: white; margin-bottom: 20px;">
            <h1>🎨 Certificate Editor</h1>
            <p>Edit your certificate before downloading</p>
        </header>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="messages">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            {{ message }}
                            <span class="close-btn" onclick="this.parentElement.style.display='none'">&times;</span>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="editor-workspace">
            <!-- Tools Panel -->
            <div class="editor-tools">
                <div class="tool-group">
                    <h3>🖊️ Drawing Tools</h3>
                    <button class="tool-button active" data-tool="pen" id="pen-tool">
                        ✏️ Pen
                    </button>
                    <button class="tool-button" data-tool="eraser" id="eraser-tool">
                        🧽 Eraser
                    </button>
                    <button class="tool-button" data-tool="text" id="text-tool">
                        📝 Text
                    </button>
                </div>
                
                <div class="tool-group">
                    <h3>🎨 Colors</h3>
                    <input type="color" id="pen-color" class="color-input" value="#000000">
                    <div style="margin-top: 10px;">
                        <button class="tool-button" onclick="setColor('#000000')">⚫ Black</button>
                        <button class="tool-button" onclick="setColor('#ff0000')">🔴 Red</button>
                        <button class="tool-button" onclick="setColor('#0000ff')">🔵 Blue</button>
                        <button class="tool-button" onclick="setColor('#008000')">🟢 Green</button>
                    </div>
                </div>
                
                <div class="tool-group">
                    <h3>📏 Size</h3>
                    <input type="range" id="brush-size" class="size-slider" min="1" max="50" value="5">
                    <div style="text-align: center;">
                        <span id="size-display">5px</span>
                    </div>
                </div>
                
                <div class="tool-group">
                    <h3>⚙️ Actions</h3>
                    <button class="tool-button" onclick="clearCanvas()">
                        🗑️ Clear All
                    </button>
                    <button class="tool-button" onclick="undoLast()">
                        ↶ Undo
                    </button>
                    <button class="tool-button" onclick="resetToOriginal()">
                        🔄 Reset
                    </button>
                </div>
            </div>

            <!-- Canvas Area -->
            <div class="canvas-container">
                <canvas id="editor-canvas"></canvas>
                
                <div class="action-buttons">
                    <button class="btn btn-secondary" onclick="goBack()">
                        ← Back to Generator
                    </button>
                    <button class="btn btn-primary" onclick="downloadCertificate()">
                        💾 Download Certificate
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Canvas setup
        const canvas = document.getElementById('editor-canvas');
        const ctx = canvas.getContext('2d');
        let isDrawing = false;
        let currentTool = 'pen';
        let currentColor = '#000000';
        let currentSize = 5;
        let undoStack = [];
        let originalImageData = null;

        // Initialize canvas with certificate image
        const certificateData = "{{ certificate.image_data }}";
        const img = new Image();
        img.onload = function() {
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            saveState(); // Save initial state
            originalImageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        };
        img.src = 'data:image/png;base64,' + certificateData;

        // Tool selection
        document.querySelectorAll('.tool-button[data-tool]').forEach(button => {
            button.addEventListener('click', function() {
                document.querySelectorAll('.tool-button[data-tool]').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                currentTool = this.dataset.tool;
                
                if (currentTool === 'text') {
                    canvas.style.cursor = 'text';
                } else if (currentTool === 'eraser') {
                    canvas.style.cursor = 'grab';
                } else {
                    canvas.style.cursor = 'crosshair';
                }
            });
        });

        // Color and size controls
        document.getElementById('pen-color').addEventListener('change', function() {
            currentColor = this.value;
        });

        document.getElementById('brush-size').addEventListener('input', function() {
            currentSize = this.value;
            document.getElementById('size-display').textContent = this.value + 'px';
        });

        // Drawing functions
        function startDrawing(e) {
            if (currentTool === 'text') {
                addText(e);
                return;
            }
            
            isDrawing = true;
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            ctx.beginPath();
            ctx.moveTo(x, y);
        }

        function draw(e) {
            if (!isDrawing) return;
            
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;
            
            ctx.lineWidth = currentSize;
            ctx.lineCap = 'round';
            
            if (currentTool === 'eraser') {
                ctx.globalCompositeOperation = 'destination-out';
            } else {
                ctx.globalCompositeOperation = 'source-over';
                ctx.strokeStyle = currentColor;
            }
            
            ctx.lineTo(x, y);
            ctx.stroke();
            ctx.beginPath();
            ctx.moveTo(x, y);
        }

        function stopDrawing() {
            if (isDrawing) {
                isDrawing = false;
                ctx.beginPath();
                saveState();
            }
        }

        function addText(e) {
            const text = prompt('Enter text:');
            if (text) {
                const rect = canvas.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                ctx.font = currentSize * 2 + 'px Arial';
                ctx.fillStyle = currentColor;
                ctx.fillText(text, x, y);
                saveState();
            }
        }

        // Event listeners
        canvas.addEventListener('mousedown', startDrawing);
        canvas.addEventListener('mousemove', draw);
        canvas.addEventListener('mouseup', stopDrawing);
        canvas.addEventListener('mouseout', stopDrawing);

        // Utility functions
        function setColor(color) {
            currentColor = color;
            document.getElementById('pen-color').value = color;
        }

        function saveState() {
            undoStack.push(ctx.getImageData(0, 0, canvas.width, canvas.height));
            if (undoStack.length > 20) {
                undoStack.shift();
            }
        }

        function undoLast() {
            if (undoStack.length > 1) {
                undoStack.pop();
                const previousState = undoStack[undoStack.length - 1];
                ctx.putImageData(previousState, 0, 0);
            }
        }

        function clearCanvas() {
            if (confirm('Are you sure you want to clear all edits?')) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                saveState();
            }
        }

        function resetToOriginal() {
            if (confirm('Reset to original certificate?')) {
                ctx.putImageData(originalImageData, 0, 0);
                saveState();
            }
        }

        function goBack() {
            window.location.href = "{{ url_for('index') }}";
        }

        function downloadCertificate() {
            const imageData = canvas.toDataURL('image/png');
            
            fetch('/save_edited_certificate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    image_data: imageData
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = data.download_url;
                } else {
                    alert('Error saving certificate: ' + data.error);
                }
            })
            .catch(error => {
                alert('Error: ' + error);
            });
        }
    </script>
</body>
</html>
