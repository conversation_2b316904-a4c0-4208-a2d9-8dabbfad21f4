from PIL import Image, ImageDraw, ImageFont
import os

def create_sample_template():
    """Create a sample certificate template"""
    # Create a new image with white background
    width, height = 1200, 800
    image = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(image)
    
    # Draw border
    border_color = '#2C5282'
    border_width = 10
    draw.rectangle([border_width, border_width, width-border_width, height-border_width], 
                   outline=border_color, width=border_width)
    
    # Draw inner decorative border
    inner_border = 30
    draw.rectangle([inner_border, inner_border, width-inner_border, height-inner_border], 
                   outline=border_color, width=3)
    
    # Try to use a nice font, fallback to default
    try:
        title_font = ImageFont.truetype("arial.ttf", 48)
        subtitle_font = ImageFont.truetype("arial.ttf", 24)
        text_font = ImageFont.truetype("arial.ttf", 18)
    except:
        try:
            title_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 48)
            subtitle_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 24)
            text_font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 18)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            text_font = ImageFont.load_default()
    
    # Add title
    title = "CERTIFICATE OF ACHIEVEMENT"
    title_bbox = draw.textbbox((0, 0), title, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    title_x = (width - title_width) // 2
    draw.text((title_x, 100), title, fill=border_color, font=title_font)
    
    # Add subtitle
    subtitle = "This is to certify that"
    subtitle_bbox = draw.textbbox((0, 0), subtitle, font=subtitle_font)
    subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
    subtitle_x = (width - subtitle_width) // 2
    draw.text((subtitle_x, 200), subtitle, fill='#4A5568', font=subtitle_font)
    
    # Add placeholder for name (this is where the name will be inserted)
    name_placeholder = "[NAME WILL BE INSERTED HERE]"
    name_bbox = draw.textbbox((0, 0), name_placeholder, font=title_font)
    name_width = name_bbox[2] - name_bbox[0]
    name_x = (width - name_width) // 2
    draw.text((name_x, 300), name_placeholder, fill='#E2E8F0', font=title_font)
    
    # Add bottom text
    bottom_text1 = "has successfully completed the requirements"
    bottom_text2 = "and is hereby awarded this certificate"
    
    bottom1_bbox = draw.textbbox((0, 0), bottom_text1, font=subtitle_font)
    bottom1_width = bottom1_bbox[2] - bottom1_bbox[0]
    bottom1_x = (width - bottom1_width) // 2
    draw.text((bottom1_x, 450), bottom_text1, fill='#4A5568', font=subtitle_font)
    
    bottom2_bbox = draw.textbbox((0, 0), bottom_text2, font=subtitle_font)
    bottom2_width = bottom2_bbox[2] - bottom2_bbox[0]
    bottom2_x = (width - bottom2_width) // 2
    draw.text((bottom2_x, 480), bottom_text2, fill='#4A5568', font=subtitle_font)
    
    # Add date and signature lines
    date_text = "Date: _______________"
    signature_text = "Signature: _______________"
    
    draw.text((100, 650), date_text, fill='#718096', font=text_font)
    draw.text((width - 300, 650), signature_text, fill='#718096', font=text_font)
    
    # Save the template
    os.makedirs('static', exist_ok=True)
    image.save('static/certificate_template.png', 'PNG')
    print("Sample certificate template created successfully!")

if __name__ == "__main__":
    create_sample_template()
