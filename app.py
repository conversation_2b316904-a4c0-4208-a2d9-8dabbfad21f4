from flask import Flask, render_template, request, send_file, flash, redirect, url_for
from PIL import Image, ImageDraw, ImageFont
import os
import io
import tempfile
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Change this to a random secret key

# Configuration
UPLOAD_FOLDER = 'static'
TEMPLATE_IMAGE = 'static/certificate_template.png'

# Ensure static directory exists
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

@app.route('/')
def index():
    """Main page with the form to enter name"""
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate_certificate():
    """Generate certificate with the provided name"""
    try:
        # Get the name from the form
        name = request.form.get('name', '').strip()
        
        if not name:
            flash('Please enter a name!', 'error')
            return redirect(url_for('index'))
        
        # Check if template exists
        if not os.path.exists(TEMPLATE_IMAGE):
            flash('Certificate template not found! Please upload a template image.', 'error')
            return redirect(url_for('index'))
        
        # Load the certificate template
        template = Image.open(TEMPLATE_IMAGE)
        
        # Create a copy to work with
        certificate = template.copy()
        
        # Create drawing context
        draw = ImageDraw.Draw(certificate)
        
        # Try to load a font (fallback to default if not available)
        try:
            # You can customize the font path and size here
            font_size = 60
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            try:
                # Try alternative font paths
                font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", 60)
            except:
                # Fallback to default font
                font = ImageFont.load_default()
        
        # Calculate text position (center of image)
        img_width, img_height = certificate.size
        
        # Get text bounding box
        bbox = draw.textbbox((0, 0), name, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        # Position text in center (you can adjust these coordinates)
        x = (img_width - text_width) // 2
        y = (img_height - text_height) // 2 + 50  # Slightly below center
        
        # Add the name to the certificate
        text_color = (0, 0, 0)  # Black color, you can change this
        draw.text((x, y), name, font=font, fill=text_color)
        
        # Save to a temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
        certificate.save(temp_file.name, 'PNG')
        temp_file.close()
        
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"certificate_{name.replace(' ', '_')}_{timestamp}.png"
        
        return send_file(
            temp_file.name,
            as_attachment=True,
            download_name=filename,
            mimetype='image/png'
        )
        
    except Exception as e:
        flash(f'Error generating certificate: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/upload_template', methods=['POST'])
def upload_template():
    """Upload a new certificate template"""
    try:
        if 'template' not in request.files:
            flash('No file selected!', 'error')
            return redirect(url_for('index'))
        
        file = request.files['template']
        
        if file.filename == '':
            flash('No file selected!', 'error')
            return redirect(url_for('index'))
        
        if file and file.filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            # Save the uploaded template
            file.save(TEMPLATE_IMAGE)
            flash('Template uploaded successfully!', 'success')
        else:
            flash('Please upload a valid image file (PNG, JPG, JPEG)', 'error')
            
    except Exception as e:
        flash(f'Error uploading template: {str(e)}', 'error')
    
    return redirect(url_for('index'))

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
