from flask import Flask, render_template, request, send_file, flash, redirect, url_for, jsonify, session
from PIL import Image, ImageDraw, ImageFont
import os
import io
import csv
import base64
from datetime import datetime
import uuid
import zipfile

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'  # Change this to a random secret key

# Configuration
UPLOAD_FOLDER = 'static'
TEMPLATE_IMAGE = 'static/certificate_template.png'
TEMP_FOLDER = 'static/temp'
BULK_FOLDER = 'static/bulk'

# Ensure directories exist
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(TEMP_FOLDER, exist_ok=True)
os.makedirs(BULK_FOLDER, exist_ok=True)

@app.route('/')
def index():
    """Main page with the form to enter name"""
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate_certificate():
    """Generate certificate with the provided name"""
    try:
        # Get the name and options from the form
        name = request.form.get('name', '').strip()
        text_color = request.form.get('text_color', '#000000')
        font_size = int(request.form.get('font_size', 60))
        edit_mode = request.form.get('edit_mode') == 'on'

        if not name:
            flash('Please enter a name!', 'error')
            return redirect(url_for('index'))

        # Check if template exists
        if not os.path.exists(TEMPLATE_IMAGE):
            flash('Certificate template not found! Please upload a template image.', 'error')
            return redirect(url_for('index'))

        # Generate certificate
        certificate_data = create_certificate(name, text_color, font_size)

        if edit_mode:
            # Save to session for editing
            session_id = str(uuid.uuid4())
            session['current_certificate'] = {
                'id': session_id,
                'name': name,
                'text_color': text_color,
                'font_size': font_size,
                'image_data': certificate_data['base64']
            }
            return redirect(url_for('editor'))
        else:
            # Direct download
            return send_file(
                io.BytesIO(certificate_data['binary']),
                as_attachment=True,
                download_name=certificate_data['filename'],
                mimetype='image/png'
            )

    except Exception as e:
        flash(f'Error generating certificate: {str(e)}', 'error')
        return redirect(url_for('index'))

def create_certificate(name, text_color='#000000', font_size=60):
    """Create a certificate with given parameters"""
    # Load the certificate template
    template = Image.open(TEMPLATE_IMAGE)
    certificate = template.copy()
    draw = ImageDraw.Draw(certificate)

    # Try to load a font
    try:
        font = ImageFont.truetype("arial.ttf", font_size)
    except:
        try:
            font = ImageFont.truetype("C:/Windows/Fonts/arial.ttf", font_size)
        except:
            font = ImageFont.load_default()

    # Calculate text position
    img_width, img_height = certificate.size
    bbox = draw.textbbox((0, 0), name, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]

    x = (img_width - text_width) // 2
    y = (img_height - text_height) // 2 + 50

    # Convert hex color to RGB
    text_color_rgb = tuple(int(text_color[i:i+2], 16) for i in (1, 3, 5))

    # Add the name to the certificate
    draw.text((x, y), name, font=font, fill=text_color_rgb)

    # Convert to base64 and binary
    img_buffer = io.BytesIO()
    certificate.save(img_buffer, 'PNG')
    img_buffer.seek(0)

    binary_data = img_buffer.getvalue()
    base64_data = base64.b64encode(binary_data).decode('utf-8')

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"certificate_{name.replace(' ', '_')}_{timestamp}.png"

    return {
        'binary': binary_data,
        'base64': base64_data,
        'filename': filename
    }

@app.route('/editor')
def editor():
    """Certificate editor page"""
    if 'current_certificate' not in session:
        flash('No certificate to edit. Please generate one first.', 'error')
        return redirect(url_for('index'))

    return render_template('editor.html', certificate=session['current_certificate'])

@app.route('/save_edited_certificate', methods=['POST'])
def save_edited_certificate():
    """Save the edited certificate"""
    try:
        # Get the edited image data from the request
        image_data = request.json.get('image_data')
        if not image_data:
            return jsonify({'error': 'No image data provided'}), 400

        # Remove data URL prefix if present
        if image_data.startswith('data:image/png;base64,'):
            image_data = image_data.split(',')[1]

        # Decode base64 image
        binary_data = base64.b64decode(image_data)

        # Generate filename
        cert_info = session.get('current_certificate', {})
        name = cert_info.get('name', 'certificate')
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"edited_certificate_{name.replace(' ', '_')}_{timestamp}.png"

        # Save to temporary file
        temp_path = os.path.join(TEMP_FOLDER, filename)
        with open(temp_path, 'wb') as f:
            f.write(binary_data)

        return jsonify({
            'success': True,
            'download_url': url_for('download_temp_file', filename=filename)
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/download_temp/<filename>')
def download_temp_file(filename):
    """Download temporary file"""
    file_path = os.path.join(TEMP_FOLDER, filename)
    if os.path.exists(file_path):
        return send_file(file_path, as_attachment=True, download_name=filename)
    else:
        flash('File not found!', 'error')
        return redirect(url_for('index'))

@app.route('/bulk_generate', methods=['POST'])
def bulk_generate():
    """Generate multiple certificates from CSV or text list"""
    try:
        names_input = request.form.get('names_list', '').strip()
        text_color = request.form.get('bulk_text_color', '#000000')
        font_size = int(request.form.get('bulk_font_size', 60))

        # Parse names from input
        names = []
        if 'names_file' in request.files and request.files['names_file'].filename:
            # Handle CSV file upload
            file = request.files['names_file']
            if file.filename.lower().endswith('.csv'):
                content = file.read().decode('utf-8')
                csv_reader = csv.reader(content.splitlines())
                for row in csv_reader:
                    if row and row[0].strip():
                        names.append(row[0].strip())
            else:
                flash('Please upload a valid CSV file', 'error')
                return redirect(url_for('index'))
        elif names_input:
            # Handle text input (one name per line)
            names = [name.strip() for name in names_input.split('\n') if name.strip()]

        if not names:
            flash('Please provide names either as text or CSV file', 'error')
            return redirect(url_for('index'))

        if len(names) > 100:
            flash('Maximum 100 certificates can be generated at once', 'error')
            return redirect(url_for('index'))

        # Generate certificates
        generated_files = []
        for name in names:
            try:
                cert_data = create_certificate(name, text_color, font_size)

                # Save to bulk folder
                bulk_filename = f"bulk_{cert_data['filename']}"
                bulk_path = os.path.join(BULK_FOLDER, bulk_filename)

                with open(bulk_path, 'wb') as f:
                    f.write(cert_data['binary'])

                generated_files.append(bulk_filename)

            except Exception as e:
                flash(f'Error generating certificate for {name}: {str(e)}', 'error')

        if generated_files:
            # Create a zip file with all certificates
            import zipfile
            zip_filename = f"certificates_bulk_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            zip_path = os.path.join(BULK_FOLDER, zip_filename)

            with zipfile.ZipFile(zip_path, 'w') as zipf:
                for filename in generated_files:
                    file_path = os.path.join(BULK_FOLDER, filename)
                    zipf.write(file_path, filename.replace('bulk_', ''))

            flash(f'Successfully generated {len(generated_files)} certificates!', 'success')
            return send_file(zip_path, as_attachment=True, download_name=zip_filename)
        else:
            flash('No certificates were generated', 'error')
            return redirect(url_for('index'))

    except Exception as e:
        flash(f'Error in bulk generation: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/upload_template', methods=['POST'])
def upload_template():
    """Upload a new certificate template"""
    try:
        if 'template' not in request.files:
            flash('No file selected!', 'error')
            return redirect(url_for('index'))

        file = request.files['template']

        if file.filename == '':
            flash('No file selected!', 'error')
            return redirect(url_for('index'))

        if file and file.filename.lower().endswith(('.png', '.jpg', '.jpeg')):
            # Save the uploaded template
            file.save(TEMPLATE_IMAGE)
            flash('Template uploaded successfully!', 'success')
        else:
            flash('Please upload a valid image file (PNG, JPG, JPEG)', 'error')

    except Exception as e:
        flash(f'Error uploading template: {str(e)}', 'error')

    return redirect(url_for('index'))

if __name__ == '__main__':
    print("Starting Certificate Generator...")
    print("Server will be available at: http://localhost:8080")
    app.run(debug=True, host='127.0.0.1', port=8080)
