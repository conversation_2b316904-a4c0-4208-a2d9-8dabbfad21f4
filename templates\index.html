<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate Generator</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1>🏆 Certificate Generator</h1>
            <p>Generate personalized certificates instantly</p>
        </header>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="messages">
                    {% for category, message in messages %}
                        <div class="alert alert-{{ category }}">
                            {{ message }}
                            <span class="close-btn" onclick="this.parentElement.style.display='none'">&times;</span>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <div class="main-content">
            <!-- Certificate Generation Form -->
            <div class="card">
                <h2>📝 Generate Certificate</h2>
                <form action="/generate" method="POST" class="form">
                    <div class="form-group">
                        <label for="name">Enter Name:</label>
                        <input
                            type="text"
                            id="name"
                            name="name"
                            placeholder="Enter the recipient's name"
                            required
                            maxlength="50"
                        >
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="text_color">Text Color:</label>
                            <input
                                type="color"
                                id="text_color"
                                name="text_color"
                                value="#000000"
                                class="color-picker"
                            >
                        </div>

                        <div class="form-group">
                            <label for="font_size">Font Size:</label>
                            <input
                                type="range"
                                id="font_size"
                                name="font_size"
                                min="20"
                                max="120"
                                value="60"
                                class="slider"
                            >
                            <span id="font_size_value">60px</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="checkbox-container">
                            <input type="checkbox" name="edit_mode" id="edit_mode">
                            <span class="checkmark"></span>
                            Enable Manual Edit Mode (Edit before download)
                        </label>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        🎯 Generate Certificate
                    </button>
                </form>
            </div>

            <!-- Bulk Certificate Generation -->
            <div class="card">
                <h2>📋 Bulk Certificate Generation</h2>
                <p class="info-text">Generate multiple certificates at once</p>
                <form action="/bulk_generate" method="POST" enctype="multipart/form-data" class="form">
                    <div class="form-group">
                        <label for="names_list">Names List (one per line):</label>
                        <textarea
                            id="names_list"
                            name="names_list"
                            placeholder="John Doe&#10;Jane Smith&#10;Bob Johnson"
                            rows="5"
                        ></textarea>
                    </div>

                    <div class="form-group">
                        <label for="names_file">Or Upload CSV File:</label>
                        <input
                            type="file"
                            id="names_file"
                            name="names_file"
                            accept=".csv"
                        >
                        <small class="help-text">CSV file should have names in the first column</small>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="bulk_text_color">Text Color:</label>
                            <input
                                type="color"
                                id="bulk_text_color"
                                name="bulk_text_color"
                                value="#000000"
                                class="color-picker"
                            >
                        </div>

                        <div class="form-group">
                            <label for="bulk_font_size">Font Size:</label>
                            <input
                                type="range"
                                id="bulk_font_size"
                                name="bulk_font_size"
                                min="20"
                                max="120"
                                value="60"
                                class="slider"
                            >
                            <span id="bulk_font_size_value">60px</span>
                        </div>
                    </div>

                    <button type="submit" class="btn btn-success">
                        📦 Generate Bulk Certificates
                    </button>
                </form>
            </div>

            <!-- Template Upload Form -->
            <div class="card">
                <h2>📤 Upload Certificate Template</h2>
                <p class="info-text">Upload your certificate template image (PNG, JPG, JPEG)</p>
                <form action="/upload_template" method="POST" enctype="multipart/form-data" class="form">
                    <div class="form-group">
                        <label for="template">Choose Template Image:</label>
                        <input
                            type="file"
                            id="template"
                            name="template"
                            accept=".png,.jpg,.jpeg"
                            required
                        >
                    </div>
                    <button type="submit" class="btn btn-secondary">
                        📁 Upload Template
                    </button>
                </form>
            </div>

            <!-- Instructions -->
            <div class="card instructions">
                <h2>📋 How to Use</h2>
                <ol>
                    <li><strong>Upload Template:</strong> First, upload your certificate template image</li>
                    <li><strong>Enter Name:</strong> Type the recipient's name in the text field</li>
                    <li><strong>Generate:</strong> Click "Generate Certificate" to create the personalized certificate</li>
                    <li><strong>Download:</strong> The certificate will be automatically downloaded to your device</li>
                </ol>

                <div class="tips">
                    <h3>💡 Tips:</h3>
                    <ul>
                        <li>Use high-resolution images (at least 1920x1080) for best quality</li>
                        <li>Leave enough space in the center of your template for the name</li>
                        <li>The name will be positioned in the center of the image</li>
                        <li>Supported formats: PNG, JPG, JPEG</li>
                    </ul>
                </div>
            </div>
        </div>

        <footer>
            <p>&copy; 2024 Certificate Generator | Made with ❤️</p>
        </footer>
    </div>

    <script>
        // Auto-hide flash messages after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                alert.style.opacity = '0';
                setTimeout(function() {
                    alert.style.display = 'none';
                }, 300);
            });
        }, 5000);

        // Font size slider updates
        document.getElementById('font_size').addEventListener('input', function(e) {
            document.getElementById('font_size_value').textContent = e.target.value + 'px';
        });

        document.getElementById('bulk_font_size').addEventListener('input', function(e) {
            document.getElementById('bulk_font_size_value').textContent = e.target.value + 'px';
        });

        // Form validation
        document.querySelector('form[action="/generate"]').addEventListener('submit', function(e) {
            const nameInput = document.getElementById('name');
            const name = nameInput.value.trim();

            if (name.length < 2) {
                e.preventDefault();
                alert('Please enter a valid name (at least 2 characters)');
                nameInput.focus();
            }
        });

        // Bulk form validation
        document.querySelector('form[action="/bulk_generate"]').addEventListener('submit', function(e) {
            const namesList = document.getElementById('names_list').value.trim();
            const namesFile = document.getElementById('names_file').files[0];

            if (!namesList && !namesFile) {
                e.preventDefault();
                alert('Please provide names either as text or upload a CSV file');
                return;
            }

            if (namesList) {
                const names = namesList.split('\n').filter(name => name.trim());
                if (names.length > 100) {
                    e.preventDefault();
                    alert('Maximum 100 names allowed at once');
                    return;
                }
            }
        });

        // File upload validation
        document.querySelectorAll('input[type="file"]').forEach(function(input) {
            input.addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    const fileSize = file.size / 1024 / 1024; // Convert to MB
                    if (fileSize > 10) {
                        alert('File size should be less than 10MB');
                        e.target.value = '';
                    }
                }
            });
        });

        // CSV file validation
        document.getElementById('names_file').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && !file.name.toLowerCase().endsWith('.csv')) {
                alert('Please upload a CSV file');
                e.target.value = '';
            }
        });

        // Clear names list when CSV is uploaded
        document.getElementById('names_file').addEventListener('change', function(e) {
            if (e.target.files[0]) {
                document.getElementById('names_list').value = '';
            }
        });

        // Clear CSV when names list is typed
        document.getElementById('names_list').addEventListener('input', function(e) {
            if (e.target.value.trim()) {
                document.getElementById('names_file').value = '';
            }
        });
    </script>
</body>
</html>
