# 🏆 Certificate Generator Website

A simple web application that automatically generates personalized certificates by adding names to certificate templates.

## ✨ Features

- **Simple Web Interface**: Easy-to-use form for entering names
- **Template Upload**: Upload your own certificate template images
- **Automatic Generation**: Instantly generates certificates with entered names
- **Download Ready**: Automatically downloads generated certificates
- **Responsive Design**: Works on desktop and mobile devices
- **Multiple Formats**: Supports PNG, JPG, and JPEG template images

## 🗂️ Project Structure

```
certificate_app/
├── app.py                          # Main Flask application
├── templates/
│   └── index.html                  # HTML form interface
├── static/
│   ├── style.css                   # CSS styling
│   └── certificate_template.png    # Sample certificate template
├── requirements.txt                # Python dependencies
├── create_sample_template.py       # Script to create sample template
└── README.md                       # This file
```

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run the Application

```bash
python app.py
```

### 3. Open in Browser

Navigate to: `http://localhost:5000`

## 📋 How to Use

1. **Upload Template** (Optional): 
   - Click "Choose Template Image" to upload your certificate template
   - Supported formats: PNG, JPG, JPEG
   - Recommended size: At least 1200x800 pixels

2. **Generate Certificate**:
   - Enter the recipient's name in the text field
   - Click "Generate Certificate"
   - The certificate will be automatically downloaded

3. **Customize Position** (Advanced):
   - Edit `app.py` to adjust text position, font size, and color
   - Modify the `x` and `y` coordinates in the `generate_certificate()` function

## 🎨 Customization

### Text Position
In `app.py`, modify these lines to change text position:
```python
x = (img_width - text_width) // 2      # Horizontal position
y = (img_height - text_height) // 2 + 50  # Vertical position
```

### Font and Style
```python
font_size = 60                         # Change font size
text_color = (0, 0, 0)                # Change text color (RGB)
```

### Template Requirements
- Leave enough space in the center for the name
- Use high-resolution images for best quality
- The name will be centered horizontally and slightly below center vertically

## 🛠️ Technical Details

- **Backend**: Python Flask
- **Image Processing**: Pillow (PIL)
- **Frontend**: HTML5, CSS3, JavaScript
- **Fonts**: Attempts to use Arial, falls back to system default

## 📝 Dependencies

- Flask 2.3.3
- Pillow 10.0.1
- Werkzeug 2.3.7

## 🔧 Configuration

### Secret Key
Change the secret key in `app.py` for production:
```python
app.secret_key = 'your-secure-secret-key-here'
```

### Port and Host
Modify the run configuration:
```python
app.run(debug=False, host='0.0.0.0', port=5000)
```

## 🎯 Sample Template

The application includes a sample certificate template. You can:
- Use it as-is for testing
- Replace it with your own design
- Create multiple templates for different certificate types

## 🚨 Troubleshooting

### Font Issues
If fonts don't load properly:
- The app will fall back to the default system font
- For Windows: Ensure Arial font is available
- For custom fonts: Add font files to the project and update the font path

### Template Upload Issues
- Ensure image files are in supported formats (PNG, JPG, JPEG)
- Check file size (recommended under 10MB)
- Verify image has sufficient space for text overlay

### Port Already in Use
If port 5000 is busy:
```bash
python app.py
# Or change port in app.py
```

## 📄 License

This project is open source and available under the MIT License.

## 🤝 Contributing

Feel free to fork this project and submit pull requests for improvements!

---

**Made with ❤️ for easy certificate generation**
