/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 300;
}

/* Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* Card Styles */
.card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.card h2 {
    color: #4a5568;
    margin-bottom: 20px;
    font-size: 1.5rem;
    font-weight: 600;
}

/* Form Styles */
.form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.form-group label {
    font-weight: 500;
    color: #4a5568;
    font-size: 0.95rem;
}

.form-group input, .form-group textarea {
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
    font-family: inherit;
}

.form-group input:focus, .form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input[type="file"] {
    padding: 8px;
    background: #f7fafc;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Color Picker Styles */
.color-picker {
    width: 60px !important;
    height: 40px !important;
    padding: 0 !important;
    border: 2px solid #e2e8f0 !important;
    border-radius: 8px !important;
    cursor: pointer;
}

/* Slider Styles */
.slider {
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    border-radius: 3px;
    background: #e2e8f0;
    outline: none;
    padding: 0 !important;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #667eea;
    cursor: pointer;
    border: none;
}

/* Checkbox Styles */
.checkbox-container {
    display: flex !important;
    flex-direction: row !important;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    font-size: 0.95rem;
    color: #4a5568;
}

.checkbox-container input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
    cursor: pointer;
}

.checkmark {
    margin-left: 5px;
}

/* Help Text */
.help-text {
    font-size: 0.8rem;
    color: #718096;
    font-style: italic;
    margin-top: 5px;
}

/* Button Styles */
.btn {
    padding: 14px 28px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    font-family: inherit;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.3);
}

.btn-success {
    background: linear-gradient(135deg, #38a169 0%, #2f855a 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(56, 161, 105, 0.3);
}

/* Alert Messages */
.messages {
    margin-bottom: 20px;
}

.alert {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    position: relative;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.alert-success {
    background: #f0fff4;
    color: #22543d;
    border-left: 4px solid #48bb78;
}

.alert-error {
    background: #fed7d7;
    color: #742a2a;
    border-left: 4px solid #e53e3e;
}

.close-btn {
    position: absolute;
    top: 15px;
    right: 20px;
    cursor: pointer;
    font-size: 1.2rem;
    font-weight: bold;
    opacity: 0.7;
}

.close-btn:hover {
    opacity: 1;
}

/* Instructions Styles */
.instructions {
    background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.instructions h2 {
    color: #2d3748;
}

.instructions ol {
    margin-left: 20px;
    margin-bottom: 20px;
}

.instructions li {
    margin-bottom: 8px;
    color: #4a5568;
}

.tips {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-top: 20px;
    border-left: 4px solid #667eea;
}

.tips h3 {
    color: #4a5568;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.tips ul {
    margin-left: 20px;
}

.tips li {
    margin-bottom: 5px;
    color: #718096;
    font-size: 0.9rem;
}

.info-text {
    color: #718096;
    font-size: 0.9rem;
    margin-bottom: 15px;
    font-style: italic;
}

/* Footer */
footer {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
    color: white;
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    header h1 {
        font-size: 2rem;
    }

    .card {
        padding: 20px;
    }

    .btn {
        padding: 12px 24px;
        font-size: 0.95rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

@media (max-width: 480px) {
    header h1 {
        font-size: 1.8rem;
    }

    .card {
        padding: 15px;
    }

    .form-group input, .form-group textarea {
        padding: 10px 14px;
    }

    .checkbox-container {
        font-size: 0.9rem;
    }

    .color-picker {
        width: 50px !important;
        height: 35px !important;
    }
}
