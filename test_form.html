<!DOCTYPE html>
<html>
<head>
    <title>Test Form</title>
</head>
<body>
    <h1>Test Form</h1>
    <form action="http://localhost:8080/generate" method="POST">
        <div>
            <label for="name">Name:</label>
            <input type="text" id="name" name="name" value="Test User" required>
        </div>
        
        <div>
            <label for="text_color">Text Color:</label>
            <input type="color" id="text_color" name="text_color" value="#000000">
        </div>
        
        <div>
            <label for="font_size">Font Size:</label>
            <input type="range" id="font_size" name="font_size" min="20" max="120" value="60">
            <span id="font_size_value">60px</span>
        </div>
        
        <div>
            <label>
                <input type="checkbox" name="edit_mode" id="edit_mode">
                Enable Manual Edit Mode
            </label>
        </div>
        
        <button type="submit">Generate Certificate</button>
    </form>
    
    <script>
        document.getElementById('font_size').addEventListener('input', function(e) {
            document.getElementById('font_size_value').textContent = e.target.value + 'px';
        });
        
        document.querySelector('form').addEventListener('submit', function(e) {
            const editMode = document.getElementById('edit_mode').checked;
            console.log('Edit mode checked:', editMode);
            alert('Edit mode: ' + editMode);
        });
    </script>
</body>
</html>
